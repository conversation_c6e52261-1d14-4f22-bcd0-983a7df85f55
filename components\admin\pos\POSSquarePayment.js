import { useState, useEffect, useRef, useCallback } from 'react'
import styles from '@/styles/admin/POS.module.css'



/**
 * POSSquarePayment component for processing Square card payments in POS
 *
 * @param {Object} props - Component props
 * @param {number} props.amount - Amount to charge
 * @param {string} props.currency - Currency code (default: AUD)
 * @param {Function} props.onSuccess - Function to call on successful payment
 * @param {Function} props.onError - Function to call on payment error
 * @param {Object} props.orderDetails - Order details for Square
 * @returns {JSX.Element}
 */
export default function POSSquarePayment({
  amount,
  currency = 'AUD',
  onSuccess,
  onError,
  orderDetails = {}
}) {
  const [paymentForm, setPaymentForm] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isProcessing, setIsProcessing] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const initializationAttemptedRef = useRef(false); // Changed from useState
  const [billingAddress, setBillingAddress] = useState({ // Added missing const/let/var for billingAddress
    addressLine1: '1455 Market St',
    addressLine2: 'Suite 600',
    locality: 'San Francisco',
    administrativeDistrictLevel1: 'CA',
    postalCode: '94103',
    country: 'US'
  })
  const [showBillingAddress, setShowBillingAddress] = useState(false)
  const containerRef = useRef(null)
  const mountedRef = useRef(false)
  const paymentFormRef = useRef(null)
  const isAttachingRef = useRef(false)
  const sdkLoadControllerRef = useRef(null);
  const selfContainerIdRef = useRef(null); // Stores the ID of the container this instance created

  // Initialize Square payment form
  const initializeSquareForm = useCallback(async () => {
    // Ensure this is always set to false on exit
    try {
      isAttachingRef.current = true

      if (!mountedRef.current) {
        console.warn('InitializeSquareForm: Component unmounted before starting.');
        isAttachingRef.current = false;
        return;
      }
      // Check initializationAttemptedRef.current AFTER mountedRef
      if (initializationAttemptedRef.current) {
        console.log('InitializeSquareForm: Already attempted in this lifecycle.');
        isAttachingRef.current = false;
        return;
      }
      if (!containerRef.current) {
        console.warn('InitializeSquareForm: containerRef.current is null.');
        isAttachingRef.current = false;
        setIsLoading(false); // Failed to initialize
        onError(new Error('Payment container reference is missing.'));
        return;
      }
      if (!window.Square) {
        console.warn('InitializeSquareForm: window.Square is not available.');
        isAttachingRef.current = false;
        setIsLoading(false); // Failed to initialize
        onError(new Error('Square SDK is not available.'));
        return;
      }

      initializationAttemptedRef.current = true; // Use ref
      setIsLoading(true)
      setErrorMessage('')

      console.log('Initializing Square payment form with enhanced DOM isolation...')

      // Get environment configuration
      const appId = process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID
      const locationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

      if (!appId || !locationId) {
        throw new Error('Square configuration missing')
      }

      // Ensure we still have a valid container reference
      if (!containerRef.current || !mountedRef.current) {
        console.warn('Container reference lost during initialization')
        return
      }

      const reactContainer = containerRef.current

      // Create a completely isolated container for Square SDK with stable ID
      const squareContainer = document.createElement('div')
      const containerId = `square-card-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`
      squareContainer.id = containerId
      squareContainer.style.cssText = `
        width: 100%;
        min-height: 60px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 16px;
        background: white;
        position: relative;
        z-index: 1;
      `

      // Add a data attribute to mark it as a Square container
      squareContainer.setAttribute('data-square-container', 'true')
      squareContainer.setAttribute('data-container-id', containerId)

      // Clear React container safely
      reactContainer.innerHTML = ''

      // Create a React-isolated wrapper that prevents DOM conflicts
      const isolationWrapper = document.createElement('div')
      isolationWrapper.style.cssText = 'width: 100%; height: 100%; position: relative;'
      isolationWrapper.setAttribute('data-react-isolation', 'true')

      // Create Square-specific container inside the isolation wrapper
      const squareWrapper = document.createElement('div')
      squareWrapper.style.cssText = 'width: 100%; height: 100%;'
      squareWrapper.setAttribute('data-square-wrapper', 'true')
      squareWrapper.appendChild(squareContainer)

      isolationWrapper.appendChild(squareWrapper)
      reactContainer.appendChild(isolationWrapper)

      // Store references for safe cleanup - use WeakMap to prevent memory leaks
      selfContainerIdRef.current = containerId
      reactContainer._squareMetadata = {
        containerId,
        squareContainer,
        squareWrapper,
        isolationWrapper,
        isSquareManaged: true
      }

      // Wait a microtask to ensure DOM is fully updated
      await new Promise(resolve => setTimeout(resolve, 0))

      if (!mountedRef.current) {
        console.warn('InitializeSquareForm: Unmounted after microtask before attaching.');
        // Cleanup will be handled by the main useEffect return function
        isAttachingRef.current = false;
        return;
      }

      // Triple-check container is still attached before proceeding
      if (!squareContainer.parentNode) {
        console.warn('Square container was removed before Square SDK could attach')
        return
      }

      // Verify the container ID hasn't changed (React interference detection)
      if (squareContainer.id !== containerId) {
        console.warn('Square container ID was modified, possible React interference')
        return
      }

      // Initialize Square payments with enhanced stability
      const payments = window.Square.payments(appId, locationId)

      // Determine if we're in sandbox environment
      const isSandbox = process.env.NODE_ENV !== 'production' ||
                       process.env.SQUARE_ENVIRONMENT === 'sandbox'

      console.log('Square environment configuration:', {
        environment: isSandbox ? 'sandbox' : 'production',
        nodeEnv: process.env.NODE_ENV,
        squareEnv: process.env.SQUARE_ENVIRONMENT,
        containerId: containerId
      })

      // Create card payment method with AVS configuration for sandbox
      const cardOptions = {
        style: {
          '.input-container': {
            borderColor: '#e0e0e0',
            borderRadius: '8px'
          },
          '.input-container.is-focus': {
            borderColor: '#4ECDC4'
          },
          '.input-container.is-error': {
            borderColor: '#dc3545'
          },
          '.message-text': {
            color: '#dc3545'
          }
        }
      }

      // For sandbox environment, configure to include billing address
      if (isSandbox) {
        setShowBillingAddress(true)
        console.log('Sandbox mode: Enabling billing address collection for AVS compatibility')
      }

      console.log(`Creating Square card object for container: ${containerId}`)
      const card = await payments.card(cardOptions)

      // Enhanced pre-attachment validation
      if (!squareContainer.parentNode || !mountedRef.current) {
        console.warn(`Square container ${containerId} was removed during card creation`)
        return
      }

      // Verify container integrity
      if (squareContainer.id !== containerId || !squareContainer.hasAttribute('data-square-container')) {
        console.warn(`Square container ${containerId} was modified during card creation`)
        return
      }

      console.log(`Square card object created, attempting to attach to isolated container: ${containerId}`)

      // Use try-catch around Square SDK attach to prevent React DOM errors
      try {
        // Use the container ID for attachment to be more specific
        await card.attach(`#${containerId}`)
        
        // Set paymentFormRef.current immediately after attach
        paymentFormRef.current = card
        
        console.log(`Square card attached successfully to isolated container: ${containerId}`)
      } catch (attachError) {
        console.error(`Failed to attach Square card to container ${containerId}:`, attachError)
        // If attachment fails, clean up and return
        try {
          if (card && typeof card.destroy === 'function') {
            await card.destroy()
          }
        } catch (destroyError) {
          console.warn('Error destroying card after failed attachment:', destroyError)
        }
        throw attachError
      }
      console.log('Payment button enabled:', !!paymentFormRef.current && !isLoading && !isProcessing)

      // Final verification after attachment
      if (!mountedRef.current || !squareContainer.parentNode) {
        console.warn(`Component unmounted or container removed after attachment: ${containerId}`)
        try {
          // Check if card exists and is an object with a destroy method
          if (card && typeof card.destroy === 'function') {
            await card.destroy()
          }
        } catch (destroyError) {
          console.warn('Error destroying card after failed attachment:', destroyError)
        }
        // If unmounted, isLoading state doesn't matter for this instance
        // but ensure isAttachingRef is false for the next potential instance.
        // isAttachingRef.current = false; // This is handled in finally
        return;
      }

      // Only set state if component is still mounted and container is stable
      if (mountedRef.current) {
        setPaymentForm(card)
        setIsLoading(false)
        console.log(`Square form initialization completed successfully: ${containerId}`)
      }
    } catch (error) {
      console.error('Square initialization error:', error)
      if (mountedRef.current) {
        // Check if the error is due to the container being removed
        if (error.message && (error.message.includes('NotFoundError') || error.message.includes('does not exist in the DOM'))) {
          setErrorMessage('Payment container disappeared. Please try again.')
        } else {
          setErrorMessage('Failed to initialize payment form. Please try again.')
        }
        setIsLoading(false)
        onError(error)
      }
    } finally {
      isAttachingRef.current = false
    }
  }, [onError]) // Removed initializationAttempted from dependencies

  // Load Square SDK
  useEffect(() => {
    mountedRef.current = true
    let loadCancelled = false; // Flag to prevent further action if unmounted during load

    const loadSquareSDK = async () => {
      if (window.Square) {
        if (mountedRef.current) initializeSquareForm()
        return
      }

      // Abort previous loading attempt if any
      if (sdkLoadControllerRef.current) {
        sdkLoadControllerRef.current.abort();
      }
      sdkLoadControllerRef.current = new AbortController();
      const signal = sdkLoadControllerRef.current.signal;

      try {
        // Determine environment and SDK URL
        const isProduction = process.env.NODE_ENV === 'production' &&
                            process.env.SQUARE_ENVIRONMENT === 'production'
        const sdkUrl = isProduction
          ? 'https://web.squarecdn.com/v1/square.js'
          : 'https://sandbox.web.squarecdn.com/v1/square.js'

        console.log('Loading Square SDK:', {
          environment: isProduction ? 'production' : 'sandbox',
          sdkUrl,
          nodeEnv: process.env.NODE_ENV,
          squareEnv: process.env.SQUARE_ENVIRONMENT
        })

        // Check if script already exists
        let script = document.querySelector('script[src*="square.js"]')

        if (!script) {
          script = document.createElement('script')
          script.src = sdkUrl
          script.async = true
          document.head.appendChild(script)
        }

        // Wait for Square SDK to load
        await new Promise((resolve, reject) => {
          if (window.Square) {
            resolve()
            return
          }

          const handleLoad = () => {
            if (signal.aborted) {
              reject(new DOMException('Load aborted', 'AbortError'));
              return;
            }
            if (window.Square) {
              console.log('Square SDK loaded successfully')
              resolve()
            } else {
              reject(new Error('Square SDK loaded but not available'))
            }
          };

          const handleError = () => {
            if (signal.aborted) {
              reject(new DOMException('Load aborted', 'AbortError'));
              return;
            }
            reject(new Error('Failed to load Square SDK'))
          };

          script.onload = handleLoad;
          script.onerror = handleError;
          
          signal.addEventListener('abort', () => {
            script.onload = null;
            script.onerror = null;
            reject(new DOMException('Load aborted', 'AbortError'));
          });

          // Timeout after 30 seconds
          const timeoutId = setTimeout(() => {
            if (signal.aborted) return;
            reject(new Error('Square SDK load timeout'))
          }, 30000); // Increased timeout

          signal.addEventListener('abort', () => clearTimeout(timeoutId));
        })

        if (mountedRef.current && !loadCancelled) {
          initializeSquareForm()
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Square SDK load aborted as component unmounted.');
          return;
        }
        console.error('Failed to load Square SDK:', error)
        if (mountedRef.current && !loadCancelled) {
          setIsLoading(false)
          setErrorMessage('Failed to load payment system. Please refresh the page.')
          onError(error)
        }
      }
    }

    loadSquareSDK()

    return () => {
      mountedRef.current = false;
      loadCancelled = true; // Signal that unmount occurred

      if (sdkLoadControllerRef.current) {
        sdkLoadControllerRef.current.abort(); // Abort SDK loading if in progress
      }

      const myInstanceContainerId = selfContainerIdRef.current;

      if (isAttachingRef.current) {
        console.warn(`Cleanup: Square SDK was potentially in an attaching phase (isAttachingRef is true for container related to ID: ${myInstanceContainerId || 'unknown'}). Proceeding with immediate cleanup as component is unmounting.`);
      }

      // Safe Square form cleanup
      if (paymentFormRef.current) {
        try {
          console.log(`Cleanup: Attempting to destroy Square form instance (for container ID: ${myInstanceContainerId || 'unknown'})`);

          // Use requestAnimationFrame to ensure Square cleanup happens after React's render cycle
          const cleanupSquareForm = () => {
            try {
              if (paymentFormRef.current && typeof paymentFormRef.current.destroy === 'function') {
                paymentFormRef.current.destroy();
                console.log('Cleanup: ✅ Square form instance destroyed successfully.');
              }
            } catch (destroyError) {
              console.warn('Cleanup: Square form destroy error (non-critical):', destroyError);
            }
          };

          // Schedule cleanup to avoid React DOM conflicts
          requestAnimationFrame(cleanupSquareForm);

        } catch (error) {
          console.error('Cleanup: Error scheduling Square form cleanup:', error);
        }

        // Clear references immediately
        paymentFormRef.current = null;
        setPaymentForm(null);
      }

      // Safe DOM reference cleanup that prevents React conflicts
      if (containerRef.current && myInstanceContainerId) {
        const reactContainer = containerRef.current;
        const metadata = reactContainer._squareMetadata;

        if (metadata && metadata.containerId === myInstanceContainerId) {
          console.log(`Cleanup: Safely clearing Square metadata for container ID ${myInstanceContainerId}`);

          // Schedule DOM cleanup to avoid React conflicts
          const safeDOMCleanup = () => {
            try {
              // Only clear the container if it still exists and belongs to us
              if (reactContainer._squareMetadata &&
                  reactContainer._squareMetadata.containerId === myInstanceContainerId) {

                // Mark as no longer Square-managed to prevent React conflicts
                if (metadata.isolationWrapper && metadata.isolationWrapper.parentNode) {
                  metadata.isolationWrapper.setAttribute('data-cleanup-safe', 'true');
                }

                // Clear metadata but let React handle actual DOM removal
                delete reactContainer._squareMetadata;
                console.log('Cleanup: ✅ Square metadata cleared safely');
              }
            } catch (cleanupError) {
              console.warn('Cleanup: DOM cleanup error (non-critical):', cleanupError);
            }
          };

          // Use setTimeout to ensure cleanup happens after React's reconciliation
          setTimeout(safeDOMCleanup, 0);

        } else if (metadata) {
          console.log(`Cleanup: Skipping cleanup - container belongs to different instance (${metadata.containerId})`);
        } else {
          console.log(`Cleanup: No Square metadata found for container ID ${myInstanceContainerId}`);
        }
      } else if (myInstanceContainerId) {
        console.warn(`Cleanup: containerRef.current is null, but instance had container ID ${myInstanceContainerId}`);
      } else {
        console.log('Cleanup: No container ID recorded by this instance');
      }
      selfContainerIdRef.current = null; // Clear for this instance in all cases at the end of cleanup
      initializationAttemptedRef.current = false; // Reset for the next mount/instance
    };
  }, [initializeSquareForm, onError]); // initializeSquareForm is a dependency

  // Retry initialization with enhanced cleanup
  const retryInitialization = useCallback(() => {
    initializationAttemptedRef.current = false; // Use ref
    setErrorMessage('')
    setIsLoading(true); // Explicitly set loading true at the start of a retry attempt

    if (isAttachingRef.current) {
      console.warn('Retry cleanup skipped: Square SDK is still attaching')
      return
    }

    // Enhanced cleanup of existing Square form
    if (paymentFormRef.current) {
      try {
        paymentFormRef.current.destroy()
        console.log('✅ Existing Square form destroyed for retry')
      } catch (error) {
        console.warn('Error destroying existing form:', error)
      }
      paymentFormRef.current = null
      setPaymentForm(null)
    }

    // Safe container cleanup for retry
    if (containerRef.current) {
      const reactContainer = containerRef.current

      // Clear Square metadata safely
      if (reactContainer._squareMetadata) {
        console.log('Cleanup: Clearing Square metadata for retry')
        delete reactContainer._squareMetadata
      }

      // Clear container content and let React rebuild
      reactContainer.innerHTML = ''
      console.log('✅ Container cleared for retry, letting React rebuild')
    }

    // Retry initialization with small delay to ensure cleanup completes
    setTimeout(() => {
      if (mountedRef.current) {
        initializeSquareForm()
      }
    }, 50)
  }, [initializeSquareForm])

  // Handle payment processing
  const handlePayment = useCallback(async () => {
    if (!paymentFormRef.current || isProcessing) {
      return
    }

    setIsProcessing(true)
    setErrorMessage('')

    try {
      // Enable payment operation protection
      const { startPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
      startPOSPaymentOperation()

      // Prepare tokenization options with billing address for sandbox
      const tokenizeOptions = {}

      // Include billing address for sandbox environment to satisfy AVS requirements
      if (showBillingAddress) {
        tokenizeOptions.verificationDetails = {
          billingContact: {
            addressLines: [billingAddress.addressLine1, billingAddress.addressLine2].filter(Boolean),
            locality: billingAddress.locality,
            administrativeDistrictLevel1: billingAddress.administrativeDistrictLevel1,
            postalCode: billingAddress.postalCode,
            country: billingAddress.country
          }
        }
        console.log('Including billing address for AVS verification:', tokenizeOptions.verificationDetails.billingContact)
      }

      // Tokenize payment method with billing address
      const result = await paymentFormRef.current.tokenize(tokenizeOptions)

      if (result.status === 'OK') {
        console.log('✅ Payment tokenized successfully with AVS data')
        // Process payment with the token
        const paymentResponse = await processPayment(result.token)

        if (paymentResponse.success) {
          onSuccess({
            paymentId: paymentResponse.paymentId,
            paymentStatus: 'COMPLETED',
            paymentDetails: {
              token: result.token,
              amount: amount,
              currency: currency,
              transactionId: paymentResponse.transactionId
            }
          })
        } else {
          throw new Error(paymentResponse.error || 'Payment processing failed')
        }
      } else {
        const errorMsg = result.errors?.[0]?.message || 'Payment tokenization failed'
        setErrorMessage(errorMsg)
        onError(new Error(errorMsg))
      }
    } catch (error) {
      console.error('Payment processing error:', error)
      setErrorMessage(error.message || 'Payment failed. Please try again.')
      onError(error)
    } finally {
      setIsProcessing(false)

      try {
        const { endPOSPaymentOperation } = await import('@/lib/pos-auth-protection')
        endPOSPaymentOperation()
      } catch (protectionError) {
        console.warn('Error ending POS payment protection:', protectionError)
      }
    }
  }, [isProcessing, showBillingAddress, billingAddress, amount, currency, onSuccess, onError])

  // Process payment using Square API
  const processPayment = async (token) => {
    console.log('🔄 Processing payment with token...')

    try {
      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          sourceId: token,
          amountMoney: {
            amount: Math.round(amount * 100), // Convert to cents
            currency: currency
          },
          orderDetails: orderDetails,
          // Include billing contact for AVS verification if available
          ...(showBillingAddress && {
            billingContact: {
              addressLines: [billingAddress.addressLine1, billingAddress.addressLine2].filter(Boolean),
              locality: billingAddress.locality,
              administrativeDistrictLevel1: billingAddress.administrativeDistrictLevel1,
              postalCode: billingAddress.postalCode,
              country: billingAddress.country
            }
          })
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Payment processing failed')
      }

      return result
    } catch (error) {
      console.error('❌ Payment processing error:', error)
      throw error
    }
  }



  // Debug render state
  console.log('🔍 POSSquarePayment render state:', {
    showBillingAddress,
    isLoading,
    paymentForm: !!paymentFormRef.current,
    squareSDKLoaded: typeof window !== 'undefined' && !!window.Square,
    initializationAttempted: initializationAttemptedRef.current // Use ref
  })

  return (
    <div className={styles.squarePaymentContainer}>
      <div className={styles.paymentFormHeader}>
        <h4>Card Payment</h4>
        <div className={styles.paymentAmount}>
          Amount: <span>${parseFloat(amount || 0).toFixed(2)} {currency}</span>
        </div>
      </div>

      {errorMessage && (
        <div className={styles.paymentError}>
          <span className={styles.errorIcon}>⚠️</span>
          <div className={styles.errorContent}>
            <div className={styles.errorText}>{errorMessage}</div>
            <button
              onClick={retryInitialization}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        </div>
      )}

      <div className={styles.cardFormContainer}>
        <div
          ref={containerRef}
          className={styles.cardForm}
          style={{
            minHeight: '60px',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            padding: '16px',
            background: 'white'
          }}
        >
          {isLoading && (
            <div className={styles.cardFormPlaceholder}>
              <div className={styles.loadingSpinner}></div>
              <p>Initializing secure payment form...</p>
            </div>
          )}
        </div>

        {/* Billing Address Form for Sandbox AVS Verification */}
        {showBillingAddress && !isLoading && (
          <div className={styles.billingAddressForm}>
            <h5>Billing Address (Required for Test Environment)</h5>
            <div className={styles.addressGrid}>
              <div className={styles.addressField}>
                <label>Address Line 1</label>
                <input
                  type="text"
                  value={billingAddress.addressLine1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine1: e.target.value}))}
                  placeholder="1455 Market St"
                />
              </div>
              <div className={styles.addressField}>
                <label>Address Line 2</label>
                <input
                  type="text"
                  value={billingAddress.addressLine2}
                  onChange={(e) => setBillingAddress(prev => ({...prev, addressLine2: e.target.value}))}
                  placeholder="Suite 600"
                />
              </div>
              <div className={styles.addressField}>
                <label>City</label>
                <input
                  type="text"
                  value={billingAddress.locality}
                  onChange={(e) => setBillingAddress(prev => ({...prev, locality: e.target.value}))}
                  placeholder="San Francisco"
                />
              </div>
              <div className={styles.addressField}>
                <label>State</label>
                <input
                  type="text"
                  value={billingAddress.administrativeDistrictLevel1}
                  onChange={(e) => setBillingAddress(prev => ({...prev, administrativeDistrictLevel1: e.target.value}))}
                  placeholder="CA"
                />
              </div>
              <div className={styles.addressField}>
                <label>ZIP Code</label>
                <input
                  type="text"
                  value={billingAddress.postalCode}
                  onChange={(e) => setBillingAddress(prev => ({...prev, postalCode: e.target.value}))}
                  placeholder="94103"
                />
              </div>
              <div className={styles.addressField}>
                <label>Country</label>
                <select
                  value={billingAddress.country}
                  onChange={(e) => setBillingAddress(prev => ({...prev, country: e.target.value}))}
                >
                  <option value="US">United States</option>
                  <option value="AU">Australia</option>
                  <option value="CA">Canada</option>
                  <option value="GB">United Kingdom</option>
                </select>
              </div>
            </div>
            <div className={styles.addressNote}>
              <p><strong>Note:</strong> This billing address is required for Square's sandbox environment to pass Address Verification System (AVS) checks. Use the pre-filled test address or enter a valid address.</p>
            </div>
          </div>
        )}
      </div>

      <div className={styles.paymentActions}>
        <button
          onClick={handlePayment}
          disabled={isProcessing || !paymentFormRef.current || isLoading}
          className={styles.processPaymentButton}
        >
          {isProcessing ? (
            <>
              <div className={styles.buttonSpinner}></div>
              Processing...
            </>
          ) : isLoading ? (
            'Initializing...'
          ) : (
            `Charge $${parseFloat(amount || 0).toFixed(2)}`
          )}
        </button>
        {!paymentFormRef.current && !isLoading && (
          <div className={styles.paymentError}>
            <span className={styles.errorIcon}>⚠️</span>
            Card form not initialized. Please wait for the form to load.
          </div>
        )}

        {errorMessage && (
          <div className={styles.errorContainer}>
            <p className={styles.errorMessage}>{errorMessage}</p>
            <button
              onClick={retryInitialization}
              className={styles.retryButton}
            >
              Retry
            </button>
          </div>
        )}
      </div>

      <div className={styles.paymentSecurity}>
        <div className={styles.securityBadges}>
          <span className={styles.securityBadge}>🔒 SSL Encrypted</span>
          <span className={styles.securityBadge}>✅ PCI Compliant</span>
          <span className={styles.securityBadge}>🛡️ Square Secure</span>
        </div>
        <p className={styles.securityText}>
          Your payment information is processed securely by Square and never stored on our servers.
        </p>
      </div>
    </div>
  )
}
